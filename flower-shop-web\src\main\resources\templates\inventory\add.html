<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Flower - Flower Shop Management</title>
    
    <!-- Bootstrap CSS -->
    <link th:href="@{/webjars/bootstrap/5.3.0/css/bootstrap.min.css}" rel="stylesheet">
    
    <!-- Include base styles -->
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #28a745 !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #28a745;
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .flower-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/dashboard}">
                🌸 Flower Shop Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        👤 Seller
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" th:href="@{/logout}">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/dashboard}">
                                📊 Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/inventory}">
                                🌺 Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders}">
                                📦 Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders/create}">
                                ➕ Create Sale
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <span class="flower-icon">🌸</span>
                        Add New Flower
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a th:href="@{/inventory}" class="btn btn-outline-secondary">
                            ← Back to Inventory
                        </a>
                    </div>
                </div>

                <!-- Add Flower Form -->
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Flower Details</h5>
                            </div>
                            <div class="card-body">
                                <form th:action="@{/inventory/add}" th:object="${flower}" method="post">
                                    <!-- Flower Name -->
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Flower Name *</label>
                                        <input type="text" class="form-control" id="name" th:field="*{name}" 
                                               placeholder="Enter flower name (e.g., Rose, Tulip)" required>
                                        <div th:if="${#fields.hasErrors('name')}" class="invalid-feedback d-block">
                                            <span th:errors="*{name}"></span>
                                        </div>
                                    </div>

                                    <!-- Price -->
                                    <div class="mb-3">
                                        <label for="price" class="form-label">Price per Unit ($) *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="price" th:field="*{price}" 
                                                   step="0.01" min="0.01" placeholder="0.00" required>
                                        </div>
                                        <div th:if="${#fields.hasErrors('price')}" class="invalid-feedback d-block">
                                            <span th:errors="*{price}"></span>
                                        </div>
                                    </div>

                                    <!-- Quantity -->
                                    <div class="mb-3">
                                        <label for="quantity" class="form-label">Initial Quantity *</label>
                                        <input type="number" class="form-control" id="quantity" th:field="*{quantity}" 
                                               min="0" placeholder="Enter quantity" required>
                                        <div class="form-text">Number of units to add to inventory</div>
                                        <div th:if="${#fields.hasErrors('quantity')}" class="invalid-feedback d-block">
                                            <span th:errors="*{quantity}"></span>
                                        </div>
                                    </div>

                                    <!-- Form Actions -->
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a th:href="@{/inventory}" class="btn btn-secondary me-md-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add Flower
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script th:src="@{/webjars/jquery/3.7.0/jquery.min.js}"></script>
    <script th:src="@{/webjars/bootstrap/5.3.0/js/bootstrap.bundle.min.js}"></script>
</body>
</html>
