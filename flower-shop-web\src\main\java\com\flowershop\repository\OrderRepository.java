package com.flowershop.repository;

import com.flowershop.model.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;
import java.util.List;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    Optional<Order> findByOrderId(String orderId);
    List<Order> findByZipCode(String zipCode);
    List<Order> findAllByOrderByOrderDateDesc();
}
