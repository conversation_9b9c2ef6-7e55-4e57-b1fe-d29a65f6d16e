// Order class definition
import java.util.List;
class Order {
    private String orderId;
    private String zipCode;
    private List<Flower> flowers;

    public Order(String orderId, String zipCode, List<Flower> flowers) {
        this.orderId = orderId;
        this.zipCode = zipCode;
        this.flowers = flowers;
    }

    public void printOrderDetails() {
        System.out.println("\nOrder ID: " + orderId);
        System.out.println("Customer ZIP Code: " + zipCode);
        System.out.println("Flowers Ordered:");
        double total = 0;
        for (Flower flower : flowers) {
            double flowerTotal = flower.getPrice() * flower.getQuantity();
            total += flowerTotal;
            System.out.println(flower.getName() + " - Quantity: " + flower.getQuantity() + ", Price: $" + flower.getPrice() + ", Total: $" + flowerTotal);
        }
        System.out.println("Total Price: $" + total);
    }
}