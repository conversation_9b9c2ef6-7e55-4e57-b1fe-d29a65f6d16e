package com.flowershop.service;

import com.flowershop.model.Flower;
import com.flowershop.model.Order;
import com.flowershop.model.OrderItem;
import com.flowershop.repository.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class OrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private InventoryService inventoryService;

    public List<Order> getAllOrders() {
        return orderRepository.findAllByOrderByOrderDateDesc();
    }

    public Optional<Order> getOrderById(Long id) {
        return orderRepository.findById(id);
    }

    public Optional<Order> getOrderByOrderId(String orderId) {
        return orderRepository.findByOrderId(orderId);
    }

    public Order createOrder(String orderId, String zipCode, List<OrderItem> orderItems) {
        // Check if order ID already exists
        if (orderRepository.findByOrderId(orderId).isPresent()) {
            throw new IllegalArgumentException("Order with ID " + orderId + " already exists");
        }

        // Validate inventory availability for all items
        for (OrderItem item : orderItems) {
            if (!inventoryService.isFlowerAvailable(item.getFlower().getId(), item.getQuantity())) {
                throw new IllegalArgumentException("Not enough stock for flower: " + item.getFlower().getName());
            }
        }

        // Create the order
        Order order = new Order(orderId, zipCode);

        // Add order items and reduce inventory
        for (OrderItem item : orderItems) {
            item.setOrder(order);
            order.addOrderItem(item);
            
            // Reduce inventory quantity
            inventoryService.reduceFlowerQuantity(item.getFlower().getId(), item.getQuantity());
        }

        return orderRepository.save(order);
    }

    public Order addItemToOrder(Long orderId, Long flowerId, Integer quantity) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new IllegalArgumentException("Order not found with id: " + orderId));

        Flower flower = inventoryService.getFlowerById(flowerId)
                .orElseThrow(() -> new IllegalArgumentException("Flower not found with id: " + flowerId));

        if (!inventoryService.isFlowerAvailable(flowerId, quantity)) {
            throw new IllegalArgumentException("Not enough stock available for: " + flower.getName());
        }

        OrderItem orderItem = new OrderItem(order, flower, quantity, flower.getPrice());
        order.addOrderItem(orderItem);

        // Reduce inventory
        inventoryService.reduceFlowerQuantity(flowerId, quantity);

        return orderRepository.save(order);
    }

    public void cancelOrder(Long orderId) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new IllegalArgumentException("Order not found with id: " + orderId));

        // Restore inventory quantities
        for (OrderItem item : order.getOrderItems()) {
            inventoryService.restoreFlowerQuantity(item.getFlower().getId(), item.getQuantity());
        }

        orderRepository.delete(order);
    }

    public List<Order> getOrdersByZipCode(String zipCode) {
        return orderRepository.findByZipCode(zipCode);
    }

    public Double calculateOrderTotal(Order order) {
        return order.getOrderItems().stream()
                .mapToDouble(OrderItem::getSubtotal)
                .sum();
    }
}
