<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} ?: 'Flower Shop Management'">Flower Shop Management</title>
    
    <!-- Bootstrap CSS -->
    <link th:href="@{/webjars/bootstrap/5.3.0/css/bootstrap.min.css}" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #28a745 !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #28a745;
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .flower-icon {
            color: #28a745;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/dashboard}">
                🌸 Flower Shop Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        👤 Seller
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" th:href="@{/logout}">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/dashboard}" 
                               th:classappend="${#request.requestURI == '/dashboard'} ? 'active' : ''">
                                📊 Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/inventory}" 
                               th:classappend="${#strings.startsWith(#request.requestURI, '/inventory')} ? 'active' : ''">
                                🌺 Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders}" 
                               th:classappend="${#strings.startsWith(#request.requestURI, '/orders')} ? 'active' : ''">
                                📦 Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders/create}" 
                               th:classappend="${#request.requestURI == '/orders/create'} ? 'active' : ''">
                                ➕ Create Sale
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Flash Messages -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Page Content -->
                <div th:fragment="content">
                    <!-- Content will be replaced by specific pages -->
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script th:src="@{/webjars/jquery/3.7.0/jquery.min.js}"></script>
    <script th:src="@{/webjars/bootstrap/5.3.0/js/bootstrap.bundle.min.js}"></script>
    
    <!-- Custom JS -->
    <script th:fragment="scripts">
        // Custom JavaScript will go here
    </script>
</body>
</html>
