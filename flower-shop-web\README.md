# 🌸 Flower Shop Management System

A complete flower shop management system with both web and desktop interfaces.

## 📁 Project Structure

```
Flower Shop/
├── index.html                    # 🏠 Main landing page
├── flower-shop-demo.html         # 🎨 Interactive frontend demo
├── flower-shop-web/              # 🌐 Spring Boot web application (THIS FOLDER)
└── Flower-Shop/                  # 📦 Original Java Swing application
```

## 🌐 Web Application Features

- 🔐 **Secure Authentication** - Login system with Spring Security
- 🌺 **Inventory Management** - Add, edit, delete, and view flowers
- 📦 **Order Management** - Create and track customer orders
- 📊 **Dashboard** - Overview of business metrics
- 📱 **Responsive Design** - Works on desktop and mobile devices
- 🎨 **Modern UI** - Clean, professional interface with Bootstrap

## 🛠️ Technology Stack

- **Backend**: Spring Boot 3.2.0
- **Frontend**: T<PERSON>meleaf, Bootstrap 5.3.0, HTML/CSS/JavaScript
- **Database**: H2 (development), MySQL (production)
- **Security**: Spring Security
- **Build Tool**: Maven

## Quick Start

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Running the Application

1. **Clone or navigate to the project directory**
   ```bash
   cd flower-shop-web
   ```

2. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

3. **Access the application**
   - Open your browser and go to: `http://localhost:8080`
   - Login with:
     - **Username**: `seller`
     - **Password**: `seller123`

### Database Console (Development)

- H2 Console: `http://localhost:8080/h2-console`
- JDBC URL: `jdbc:h2:mem:flowershop`
- Username: `sa`
- Password: (leave empty)

## Application Structure

```
src/main/java/com/flowershop/
├── config/          # Configuration classes
├── controller/      # Web controllers
├── model/          # Entity classes
├── repository/     # Data repositories
└── service/        # Business logic services

src/main/resources/
├── templates/      # Thymeleaf templates
├── static/         # CSS, JS, images
└── application.properties
```

## Default Data

The application comes pre-loaded with sample flowers:
- Rose ($2.50, 10 units)
- Tulip ($1.80, 15 units)
- Daisy ($1.20, 20 units)
- Sunflower ($1.30, 30 units)
- Lily ($3.00, 8 units)
- Orchid ($5.00, 5 units)

## Configuration

### Using MySQL Database

To switch from H2 to MySQL, update `application.properties`:

```properties
# Comment out H2 configuration and uncomment MySQL:
spring.datasource.url=********************************************
spring.datasource.username=root
spring.datasource.password=1234
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=update
```

### Customizing Authentication

Edit `SecurityConfig.java` to modify login credentials or add more users.

## API Endpoints

- `/` - Redirects to dashboard
- `/login` - Login page
- `/dashboard` - Main dashboard
- `/inventory` - Inventory management
- `/inventory/add` - Add new flower
- `/inventory/edit/{id}` - Edit flower
- `/orders` - Order management
- `/orders/create` - Create new order

## Development

### Building for Production

```bash
mvn clean package
java -jar target/flower-shop-web-1.0.0.jar
```

### Running Tests

```bash
mvn test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions or issues, please create an issue in the repository.
