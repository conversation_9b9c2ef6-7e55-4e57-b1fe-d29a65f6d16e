<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Flower Shop Management</title>
    
    <!-- Bootstrap CSS -->
    <link th:href="@{/webjars/bootstrap/5.3.0/css/bootstrap.min.css}" rel="stylesheet">
    
    <!-- Include base styles -->
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #28a745 !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #28a745;
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            transition: transform 0.2s ease-in-out;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        .quick-action-card {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .quick-action-card:hover {
            border-color: #28a745;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/dashboard}">
                🌸 Flower Shop Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        👤 Seller
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" th:href="@{/logout}">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/dashboard}">
                                📊 Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/inventory}">
                                🌺 Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders}">
                                📦 Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders/create}">
                                ➕ Create Sale
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Flowers</div>
                                        <div class="h5 mb-0 font-weight-bold">12</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">🌺</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Orders</div>
                                        <div class="h5 mb-0 font-weight-bold">8</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">📦</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Low Stock</div>
                                        <div class="h5 mb-0 font-weight-bold">3</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">⚠️</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Revenue</div>
                                        <div class="h5 mb-0 font-weight-bold">$1,234</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">💰</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h3>Quick Actions</h3>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">🌸</div>
                                <h5 class="card-title">Add New Flower</h5>
                                <p class="card-text">Add a new flower to your inventory</p>
                                <a th:href="@{/inventory/add}" class="btn btn-primary">Add Flower</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">🛒</div>
                                <h5 class="card-title">Create Sale</h5>
                                <p class="card-text">Process a new customer order</p>
                                <a th:href="@{/orders/create}" class="btn btn-primary">Create Sale</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">📋</div>
                                <h5 class="card-title">View Inventory</h5>
                                <p class="card-text">Manage your flower inventory</p>
                                <a th:href="@{/inventory}" class="btn btn-primary">View Inventory</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">📊</div>
                                <h5 class="card-title">Order History</h5>
                                <p class="card-text">View all past orders and sales</p>
                                <a th:href="@{/orders}" class="btn btn-primary">View Orders</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Recent Activity</h5>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>New order created</strong><br>
                                            <small class="text-muted">Order #ORD001 - 2 hours ago</small>
                                        </div>
                                        <span class="badge bg-success rounded-pill">New</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Inventory updated</strong><br>
                                            <small class="text-muted">Rose quantity updated - 3 hours ago</small>
                                        </div>
                                        <span class="badge bg-info rounded-pill">Updated</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Low stock alert</strong><br>
                                            <small class="text-muted">Tulip stock is running low - 5 hours ago</small>
                                        </div>
                                        <span class="badge bg-warning rounded-pill">Alert</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script th:src="@{/webjars/jquery/3.7.0/jquery.min.js}"></script>
    <script th:src="@{/webjars/bootstrap/5.3.0/js/bootstrap.bundle.min.js}"></script>
</body>
</html>
