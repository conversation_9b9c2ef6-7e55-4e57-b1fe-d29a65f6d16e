import java.util.ArrayList;
import java.util.List;

class OrderManager implements OrderActions {
    private List<Order> orderHistory;

    public OrderManager() {
        orderHistory = new ArrayList<>();
    }

    @Override
    public void createOrder(String orderId, String zipCode, List<Flower> selectedFlowers) {
        orderHistory.add(new Order(orderId, zipCode, selectedFlowers));
        System.out.println("Order " + orderId + " created for ZIP Code " + zipCode);
    }

    public void viewOrderHistory() {
        System.out.println("\n--- Order History ---");
        if (orderHistory.isEmpty()) {
            System.out.println("No orders found.");
        } else {
            for (Order order : orderHistory) {
                order.printOrderDetails();
            }
        }
        System.out.println("----------------------");
    }
}
