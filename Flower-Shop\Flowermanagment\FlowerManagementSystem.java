import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import javax.swing.*;

public class FlowerManagementSystem {
    private InventoryManager inventoryManager = new InventoryManager();
    private OrderManager orderManager = new OrderManager();
    private LoginManager loginManager = new LoginManager();

    public FlowerManagementSystem() {}

    public void run() {
        if (!login()) {
            JOptionPane.showMessageDialog(null, "Invalid login attempt. Exiting system.");
            System.exit(0);
        } else {
            showMainMenu();
        }
    }
        //abst
    private boolean login() {
        String username = JOptionPane.showInputDialog("Enter username (Only 'seller' is allowed):");
        if (username == null) return false;
        String password = JOptionPane.showInputDialog("Enter password:");
        return loginManager.login(username, password);
    }

    private void showMainMenu() {
        JFrame frame = new JFrame("Flower Management System");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        JPanel panel = new JPanel(new GridLayout(5, 1, 10, 10));
        
        String[] options = {"Create Sale", "Edit Inventory", "View Order History", "View Inventory", "Exit"};
        for (String option : options) {
            JButton btn = new JButton(option);
            btn.addActionListener(e -> handleMenuSelection(option, frame));
            panel.add(btn);
        }

        frame.add(panel);
        frame.pack();
        frame.setVisible(true);
    }

    private void handleMenuSelection(String option, JFrame parent) {
        switch (option) {
            case "Create Sale":
                createSale();
                break;
            case "Edit Inventory":
                editInventory();
                break;
            case "View Order History":
                viewOrderHistory();
                break;
            case "View Inventory":
                viewInventory();
                break;
            case "Exit":
                parent.dispose();
                System.exit(0);
        }
    }

    private void createSale() {
        List<Flower> flowers = new ArrayList<>();
        String orderId = JOptionPane.showInputDialog("Enter order ID:");
        String customerId = JOptionPane.showInputDialog("Enter customer ID:");

        while (true) {
            String flowerName = JOptionPane.showInputDialog("Enter flower name (or 'done' to finish):");
            if (flowerName == null || flowerName.equalsIgnoreCase("done")) break;

            String quantityStr = JOptionPane.showInputDialog("Enter quantity:");
            try {
                int quantity = Integer.parseInt(quantityStr);
                Flower flower = inventoryManager.getFlowerByName(flowerName);
                
                if (flower != null && flower.getQuantity() >= quantity) {
                    flowers.add(new Flower(flowerName, flower.getPrice(), quantity));
                    flower.setQuantity(flower.getQuantity() - quantity);
                } else {
                    JOptionPane.showMessageDialog(null, "Invalid flower or quantity");
                }
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(null, "Invalid quantity");
            }
        }

        orderManager.createOrder(orderId, customerId, flowers);
        displayReceipt(orderId, customerId, flowers);
    }

    private void displayReceipt(String orderId, String customerId, List<Flower> flowers) {
        StringBuilder receipt = new StringBuilder("--- Receipt ---\n");
        double total = 0;
        
        for (Flower flower : flowers) {
            double subtotal = flower.getPrice() * flower.getQuantity();
            receipt.append(String.format("%s x%d @ $%.2f = $%.2f\n", 
                flower.getName(), flower.getQuantity(), flower.getPrice(), subtotal));
            total += subtotal;
        }
        
        receipt.append(String.format("\nTotal: $%.2f", total));
        showScrollableMessage("Receipt", receipt.toString());
    }

    private void editInventory() {
        JPanel panel = new JPanel(new GridLayout(4, 1));
        String[] options = {"Add Flower", "Remove Flower", "Update Flower", "Back"};
        JComboBox<String> combo = new JComboBox<>(options);
        panel.add(combo);

        int result = JOptionPane.showConfirmDialog(null, panel, "Edit Inventory", 
            JOptionPane.OK_CANCEL_OPTION, JOptionPane.PLAIN_MESSAGE);
        
        if (result == JOptionPane.OK_OPTION) {
            switch (combo.getSelectedIndex()) {
                case 0: addFlower(); break;
                case 1: removeFlower(); break;
                case 2: updateFlower(); break;
            }
        }
    }

    //abst - Hides: Database SQL, error handling, validation, Exposes: Just a success message.
    private void addFlower() {
        JPanel panel = new JPanel(new GridLayout(3, 2));
        JTextField nameField = new JTextField();
        JTextField priceField = new JTextField();
        JTextField qtyField = new JTextField();

        panel.add(new JLabel("Name:"));
        panel.add(nameField);
        panel.add(new JLabel("Price:"));
        panel.add(priceField);
        panel.add(new JLabel("Quantity:"));
        panel.add(qtyField);

        int result = JOptionPane.showConfirmDialog(null, panel, "Add Flower", 
            JOptionPane.OK_CANCEL_OPTION, JOptionPane.PLAIN_MESSAGE);
        
        if (result == JOptionPane.OK_OPTION) {
            // Database insert logic here
            JOptionPane.showMessageDialog(null, "Flower added");
        }
    }

    private void removeFlower() {
        String name = JOptionPane.showInputDialog("Enter flower name to remove:");
        if (inventoryManager.removeFlower(name)) {
            JOptionPane.showMessageDialog(null, "Flower removed");
        } else {
            JOptionPane.showMessageDialog(null, "Flower not found");
        }
    }

    //abst
    private void updateFlower() {
        String name = JOptionPane.showInputDialog("Enter flower name to update:");
        Flower flower = inventoryManager.getFlowerByName(name); //Hides: How flowers are fetched (search algorithm, data source).
        
        if (flower != null) {
            JPanel panel = new JPanel(new GridLayout(2, 2));
            JTextField priceField = new JTextField(String.valueOf(flower.getPrice()));
            JTextField qtyField = new JTextField(String.valueOf(flower.getQuantity()));
            
            panel.add(new JLabel("New Price:"));
            panel.add(priceField);
            panel.add(new JLabel("New Quantity:"));
            panel.add(qtyField);

            if (JOptionPane.showConfirmDialog(null, panel, "Update Flower", 
                JOptionPane.OK_CANCEL_OPTION) == JOptionPane.OK_OPTION) {
                
                flower.setPrice(Double.parseDouble(priceField.getText())); //Whether the change saves to a DB or just memory, Abstracts how price updates propagate
                flower.setQuantity(Integer.parseInt(qtyField.getText()));
                JOptionPane.showMessageDialog(null, "Flower updated");
            }
        } else {
            JOptionPane.showMessageDialog(null, "Flower not found");
        }
    }

    private void viewOrderHistory() {
        //List<Order> orders = orderManager.getOrderHistory();
        //StringBuilder sb = new StringBuilder("--- Order History ---\n");
        
        // for (Order order : orders) {
        //     sb.append(order.toString()).append("\n");
        // }
        
        showScrollableMessage("Order History", "Order history not implemented");
    }

    private void viewInventory() {
        List<Flower> inventory = inventoryManager.getInventory();
        StringBuilder sb = new StringBuilder("--- Inventory ---\n");
        
        for (Flower flower : inventory) {
            sb.append(String.format("%s - $%.2f x%d\n", 
                flower.getName(), flower.getPrice(), flower.getQuantity()));
        }
        
        showScrollableMessage("Inventory", sb.toString());
    }

    private void showScrollableMessage(String title, String message) {
        JTextArea textArea = new JTextArea(message);
        JScrollPane scrollPane = new JScrollPane(textArea);
        textArea.setEditable(false);
        scrollPane.setPreferredSize(new Dimension(400, 300));
        JOptionPane.showMessageDialog(null, scrollPane, title, JOptionPane.INFORMATION_MESSAGE);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> new FlowerManagementSystem().run());
    }
}