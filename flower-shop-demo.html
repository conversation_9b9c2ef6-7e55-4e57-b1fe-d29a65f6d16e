<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flower Shop Management - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #28a745 !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #28a745;
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            transition: transform 0.2s ease-in-out;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        .quick-action-card {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .quick-action-card:hover {
            border-color: #28a745;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
        }
        
        .demo-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- Demo Banner -->
    <div class="demo-banner">
        <h4>🌸 Flower Shop Management System - Frontend Demo</h4>
        <p class="mb-0">This is a preview of the web interface. The full Spring Boot application is ready to run!</p>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                🌸 Flower Shop Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <span id="current-user">👤 Seller</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="switchToCustomer()">🛒 Switch to Customer View</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">🚪 Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <!-- Seller Menu -->
                    <ul class="nav flex-column" id="seller-menu">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                📊 Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#inventory">
                                🌺 Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#orders">
                                📦 Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#create-sale">
                                ➕ Create Sale
                            </a>
                        </li>
                    </ul>

                    <!-- Customer Menu (Hidden by default) -->
                    <ul class="nav flex-column" id="customer-menu" style="display: none;">
                        <li class="nav-item">
                            <a class="nav-link active" href="#customer-shop">
                                🛒 Shop Flowers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#customer-cart">
                                🛍️ My Cart
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#customer-orders">
                                📋 My Orders
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Flowers</div>
                                        <div class="h5 mb-0 font-weight-bold">6</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">🌺</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Orders</div>
                                        <div class="h5 mb-0 font-weight-bold">12</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">📦</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Low Stock</div>
                                        <div class="h5 mb-0 font-weight-bold">2</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">⚠️</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Revenue</div>
                                        <div class="h5 mb-0 font-weight-bold">$2,847</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">💰</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h3>Quick Actions</h3>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">🌸</div>
                                <h5 class="card-title">Add New Flower</h5>
                                <p class="card-text">Add a new flower to your inventory</p>
                                <button class="btn btn-primary" onclick="showAddFlowerForm()">Add Flower</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">🛒</div>
                                <h5 class="card-title">Create Sale</h5>
                                <p class="card-text">Process a new customer order</p>
                                <button class="btn btn-primary" onclick="showCreateSaleForm()">Create Sale</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">📋</div>
                                <h5 class="card-title">View Inventory</h5>
                                <p class="card-text">Manage your flower inventory</p>
                                <button class="btn btn-primary" onclick="showInventory()">View Inventory</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">📊</div>
                                <h5 class="card-title">Order History</h5>
                                <p class="card-text">View all past orders and sales</p>
                                <button class="btn btn-primary" onclick="showOrderHistory()">View Orders</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Flower Form (Hidden by default) -->
                <div id="add-flower-section" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">🌸 Add New Flower</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="hideSections()">← Back to Dashboard</button>
                        </div>
                        <div class="card-body">
                            <div class="row justify-content-center">
                                <div class="col-md-6">
                                    <form onsubmit="addFlower(event)">
                                        <div class="mb-3">
                                            <label for="flowerName" class="form-label">Flower Name *</label>
                                            <input type="text" class="form-control" id="flowerName" placeholder="Enter flower name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="flowerPrice" class="form-label">Price per Unit ($) *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="flowerPrice" step="0.01" min="0.01" placeholder="0.00" required>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="flowerQuantity" class="form-label">Initial Quantity *</label>
                                            <input type="number" class="form-control" id="flowerQuantity" min="0" placeholder="Enter quantity" required>
                                        </div>
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <button type="button" class="btn btn-secondary me-md-2" onclick="hideSections()">Cancel</button>
                                            <button type="submit" class="btn btn-primary">Add Flower</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Sale Form (Hidden by default) -->
                <div id="create-sale-section" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">🛒 Create New Sale</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="hideSections()">← Back to Dashboard</button>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <form onsubmit="createSale(event)">
                                        <div class="mb-3">
                                            <label for="orderId" class="form-label">Order ID *</label>
                                            <input type="text" class="form-control" id="orderId" placeholder="Enter order ID" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="zipCode" class="form-label">ZIP Code *</label>
                                            <input type="text" class="form-control" id="zipCode" placeholder="Enter ZIP code" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="flowerSelect" class="form-label">Select Flower *</label>
                                            <select class="form-control" id="flowerSelect" required>
                                                <option value="">Choose a flower...</option>
                                                <option value="rose">Rose - $2.50</option>
                                                <option value="tulip">Tulip - $1.80</option>
                                                <option value="daisy">Daisy - $1.20</option>
                                                <option value="sunflower">Sunflower - $1.30</option>
                                                <option value="lily">Lily - $3.00</option>
                                                <option value="orchid">Orchid - $5.00</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="saleQuantity" class="form-label">Quantity *</label>
                                            <input type="number" class="form-control" id="saleQuantity" min="1" placeholder="Enter quantity" required>
                                        </div>
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <button type="button" class="btn btn-secondary me-md-2" onclick="hideSections()">Cancel</button>
                                            <button type="submit" class="btn btn-primary">Create Sale</button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Order Summary</h6>
                                            <div id="order-summary">
                                                <p class="text-muted">Select items to see order summary</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order History (Hidden by default) -->
                <div id="order-history-section" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">📊 Order History</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="hideSections()">← Back to Dashboard</button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Date</th>
                                            <th>ZIP Code</th>
                                            <th>Items</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>ORD001</strong></td>
                                            <td>2024-01-15</td>
                                            <td>12345</td>
                                            <td>Rose x2, Tulip x3</td>
                                            <td><span class="fw-bold text-success">$10.40</span></td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ORD002</strong></td>
                                            <td>2024-01-14</td>
                                            <td>67890</td>
                                            <td>Sunflower x5</td>
                                            <td><span class="fw-bold text-success">$6.50</span></td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ORD003</strong></td>
                                            <td>2024-01-13</td>
                                            <td>54321</td>
                                            <td>Orchid x1, Lily x2</td>
                                            <td><span class="fw-bold text-success">$11.00</span></td>
                                            <td><span class="badge bg-warning">Processing</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ORD004</strong></td>
                                            <td>2024-01-12</td>
                                            <td>98765</td>
                                            <td>Daisy x10</td>
                                            <td><span class="fw-bold text-success">$12.00</span></td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Shopping Interface (Hidden by default) -->
                <div id="customer-shop-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">🛒 Shop Beautiful Flowers</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button class="btn btn-outline-primary" onclick="showCustomerCart()">
                                🛍️ View Cart (<span id="cart-count">0</span>)
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-4" data-flower="rose">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div style="font-size: 4rem;">🌹</div>
                                    <h5 class="card-title">Rose</h5>
                                    <p class="card-text">Beautiful red roses perfect for any occasion</p>
                                    <p class="h4 text-success">$2.50</p>
                                    <p class="text-muted">Stock: 10 available</p>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" min="1" max="10" value="1" id="rose-qty">
                                        <button class="btn btn-primary" onclick="addToCart('rose', 'Rose', 2.50)">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4" data-flower="tulip">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div style="font-size: 4rem;">🌷</div>
                                    <h5 class="card-title">Tulip</h5>
                                    <p class="card-text">Elegant tulips in vibrant colors</p>
                                    <p class="h4 text-success">$1.80</p>
                                    <p class="text-muted">Stock: 15 available</p>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" min="1" max="15" value="1" id="tulip-qty">
                                        <button class="btn btn-primary" onclick="addToCart('tulip', 'Tulip', 1.80)">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4" data-flower="daisy">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div style="font-size: 4rem;">🌼</div>
                                    <h5 class="card-title">Daisy</h5>
                                    <p class="card-text">Cheerful daisies to brighten your day</p>
                                    <p class="h4 text-success">$1.20</p>
                                    <p class="text-muted">Stock: 20 available</p>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" min="1" max="20" value="1" id="daisy-qty">
                                        <button class="btn btn-primary" onclick="addToCart('daisy', 'Daisy', 1.20)">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4" data-flower="sunflower">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div style="font-size: 4rem;">🌻</div>
                                    <h5 class="card-title">Sunflower</h5>
                                    <p class="card-text">Bright sunflowers full of sunshine</p>
                                    <p class="h4 text-success">$1.30</p>
                                    <p class="text-muted">Stock: 30 available</p>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" min="1" max="30" value="1" id="sunflower-qty">
                                        <button class="btn btn-primary" onclick="addToCart('sunflower', 'Sunflower', 1.30)">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4" data-flower="lily">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div style="font-size: 4rem;">🌺</div>
                                    <h5 class="card-title">Lily</h5>
                                    <p class="card-text">Elegant lilies with amazing fragrance</p>
                                    <p class="h4 text-success">$3.00</p>
                                    <p class="text-muted">Stock: 8 available</p>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" min="1" max="8" value="1" id="lily-qty">
                                        <button class="btn btn-primary" onclick="addToCart('lily', 'Lily', 3.00)">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4" data-flower="orchid">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div style="font-size: 4rem;">🌸</div>
                                    <h5 class="card-title">Orchid</h5>
                                    <p class="card-text">Exotic orchids for special occasions</p>
                                    <p class="h4 text-success">$5.00</p>
                                    <p class="text-muted">Stock: 5 available</p>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" min="1" max="5" value="1" id="orchid-qty">
                                        <button class="btn btn-primary" onclick="addToCart('orchid', 'Orchid', 5.00)">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Cart (Hidden by default) -->
                <div id="customer-cart-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">🛍️ My Shopping Cart</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button class="btn btn-outline-secondary me-2" onclick="showCustomerShop()">← Continue Shopping</button>
                            <button class="btn btn-success" onclick="customerCheckout()" id="checkout-btn" disabled>Checkout</button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div id="cart-items">
                                <div class="text-center py-5">
                                    <div style="font-size: 4rem; color: #dee2e6;">🛒</div>
                                    <h4 class="text-muted">Your cart is empty</h4>
                                    <p class="text-muted">Add some beautiful flowers to get started!</p>
                                    <button class="btn btn-primary" onclick="showCustomerShop()">Start Shopping</button>
                                </div>
                            </div>
                            <div id="cart-total" style="display: none;" class="border-top pt-3 mt-3">
                                <div class="row">
                                    <div class="col-md-8"></div>
                                    <div class="col-md-4">
                                        <h4>Total: $<span id="total-amount">0.00</span></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sample Inventory Table (Hidden by default) -->
                <div id="inventory-section" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">🌺 Current Inventory</h5>
                            <button class="btn btn-primary btn-sm" onclick="showAddFlowerForm()">Add New Flower</button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Flower</th>
                                            <th>Price</th>
                                            <th>Quantity</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌹</span>
                                                    <strong>Rose</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$2.50</span></td>
                                            <td><span class="fw-bold">10</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editFlower('Rose')">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFlower('Rose')">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌷</span>
                                                    <strong>Tulip</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$1.80</span></td>
                                            <td><span class="fw-bold">15</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editFlower('Tulip')">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFlower('Tulip')">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌼</span>
                                                    <strong>Daisy</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$1.20</span></td>
                                            <td><span class="fw-bold">20</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editFlower('Daisy')">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFlower('Daisy')">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌻</span>
                                                    <strong>Sunflower</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$1.30</span></td>
                                            <td><span class="fw-bold">30</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editFlower('Sunflower')">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFlower('Sunflower')">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌺</span>
                                                    <strong>Lily</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$3.00</span></td>
                                            <td><span class="fw-bold">8</span></td>
                                            <td><span class="badge bg-warning">Low Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editFlower('Lily')">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFlower('Lily')">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌸</span>
                                                    <strong>Orchid</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$5.00</span></td>
                                            <td><span class="fw-bold">5</span></td>
                                            <td><span class="badge bg-danger">Very Low</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editFlower('Orchid')">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFlower('Orchid')">Delete</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentRole = 'seller';
        let cart = [];

        // Hide all sections
        function hideSections() {
            document.getElementById('inventory-section').style.display = 'none';
            document.getElementById('add-flower-section').style.display = 'none';
            document.getElementById('create-sale-section').style.display = 'none';
            document.getElementById('order-history-section').style.display = 'none';
            document.getElementById('customer-shop-section').style.display = 'none';
            document.getElementById('customer-cart-section').style.display = 'none';
        }

        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                alert('👋 Logged out successfully!\n\nIn the real application, this would redirect to the login page.');
                // Reset to seller view
                currentRole = 'seller';
                cart = [];
                updateCartCount();
                showSellerInterface();
                hideSections();
            }
        }

        // Switch to customer view
        function switchToCustomer() {
            currentRole = 'customer';
            document.getElementById('current-user').innerHTML = '🛒 Customer';
            showCustomerInterface();
            showCustomerShop();
            alert('🛒 Switched to Customer View!\n\nYou can now browse and shop for flowers. Use the dropdown menu to switch back to Seller view.');
        }

        // Switch back to seller view
        function switchToSeller() {
            currentRole = 'seller';
            document.getElementById('current-user').innerHTML = '👤 Seller';
            showSellerInterface();
            hideSections();
            alert('👤 Switched back to Seller View!\n\nYou can now manage inventory and process orders.');
        }

        // Show seller interface
        function showSellerInterface() {
            document.getElementById('seller-menu').style.display = 'block';
            document.getElementById('customer-menu').style.display = 'none';
            // Update dropdown menu
            const dropdown = document.querySelector('.dropdown-menu');
            dropdown.innerHTML = `
                <li><a class="dropdown-item" href="#" onclick="switchToCustomer()">🛒 Switch to Customer View</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="logout()">🚪 Logout</a></li>
            `;
        }

        // Show customer interface
        function showCustomerInterface() {
            document.getElementById('seller-menu').style.display = 'none';
            document.getElementById('customer-menu').style.display = 'block';
            // Update dropdown menu
            const dropdown = document.querySelector('.dropdown-menu');
            dropdown.innerHTML = `
                <li><a class="dropdown-item" href="#" onclick="switchToSeller()">👤 Switch to Seller View</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="logout()">🚪 Logout</a></li>
            `;
        }

        // Show inventory section
        function showInventory() {
            hideSections();
            document.getElementById('inventory-section').style.display = 'block';
            document.getElementById('inventory-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Show add flower form
        function showAddFlowerForm() {
            hideSections();
            document.getElementById('add-flower-section').style.display = 'block';
            document.getElementById('add-flower-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Show create sale form
        function showCreateSaleForm() {
            hideSections();
            document.getElementById('create-sale-section').style.display = 'block';
            document.getElementById('create-sale-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Show order history
        function showOrderHistory() {
            hideSections();
            document.getElementById('order-history-section').style.display = 'block';
            document.getElementById('order-history-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Add flower function
        function addFlower(event) {
            event.preventDefault();
            const name = document.getElementById('flowerName').value;
            const price = document.getElementById('flowerPrice').value;
            const quantity = document.getElementById('flowerQuantity').value;

            alert(`✅ Flower Added Successfully!\n\nName: ${name}\nPrice: $${price}\nQuantity: ${quantity}\n\nIn the real application, this would be saved to the database.`);

            // Reset form
            document.getElementById('flowerName').value = '';
            document.getElementById('flowerPrice').value = '';
            document.getElementById('flowerQuantity').value = '';
        }

        // Create sale function
        function createSale(event) {
            event.preventDefault();
            const orderId = document.getElementById('orderId').value;
            const zipCode = document.getElementById('zipCode').value;
            const flower = document.getElementById('flowerSelect').value;
            const quantity = document.getElementById('saleQuantity').value;

            const flowerNames = {
                'rose': 'Rose',
                'tulip': 'Tulip',
                'daisy': 'Daisy',
                'sunflower': 'Sunflower',
                'lily': 'Lily',
                'orchid': 'Orchid'
            };

            const prices = {
                'rose': 2.50,
                'tulip': 1.80,
                'daisy': 1.20,
                'sunflower': 1.30,
                'lily': 3.00,
                'orchid': 5.00
            };

            const total = (prices[flower] * quantity).toFixed(2);

            alert(`🛒 Sale Created Successfully!\n\nOrder ID: ${orderId}\nZIP Code: ${zipCode}\nFlower: ${flowerNames[flower]}\nQuantity: ${quantity}\nTotal: $${total}\n\nIn the real application, this would update inventory and create an order record.`);

            // Reset form
            document.getElementById('orderId').value = '';
            document.getElementById('zipCode').value = '';
            document.getElementById('flowerSelect').value = '';
            document.getElementById('saleQuantity').value = '';
        }

        // Update sidebar active state
        function updateSidebarActive(activeId) {
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            if (activeId) {
                document.querySelector(`a[href="#${activeId}"]`).classList.add('active');
            } else {
                document.querySelector('a[href="#dashboard"]').classList.add('active');
            }
        }

        // Add click handlers to sidebar links
        document.addEventListener('DOMContentLoaded', function() {
            // Seller menu handlers
            document.querySelector('a[href="#dashboard"]').addEventListener('click', function(e) {
                e.preventDefault();
                hideSections();
                updateSidebarActive('dashboard');
            });

            document.querySelector('a[href="#inventory"]').addEventListener('click', function(e) {
                e.preventDefault();
                showInventory();
                updateSidebarActive('inventory');
            });

            document.querySelector('a[href="#orders"]').addEventListener('click', function(e) {
                e.preventDefault();
                showOrderHistory();
                updateSidebarActive('orders');
            });

            document.querySelector('a[href="#create-sale"]').addEventListener('click', function(e) {
                e.preventDefault();
                showCreateSaleForm();
                updateSidebarActive('create-sale');
            });

            // Customer menu handlers
            document.querySelector('a[href="#customer-shop"]').addEventListener('click', function(e) {
                e.preventDefault();
                showCustomerShop();
            });

            document.querySelector('a[href="#customer-cart"]').addEventListener('click', function(e) {
                e.preventDefault();
                showCustomerCart();
            });

            document.querySelector('a[href="#customer-orders"]').addEventListener('click', function(e) {
                e.preventDefault();
                alert('🚧 Customer Order History\n\nThis feature would show the customer\'s past orders and delivery status.');
            });
        });

        // Demo delete function
        function deleteFlower(flowerName) {
            if (confirm(`Are you sure you want to delete ${flowerName} from inventory?`)) {
                alert(`🗑️ ${flowerName} has been deleted from inventory!\n\nIn the real application, this would remove the flower from the database.`);
            }
        }

        // Demo edit function
        function editFlower(flowerName) {
            alert(`✏️ Edit ${flowerName}\n\nIn the real application, this would open an edit form with the current flower details.`);
        }

        // Customer shopping functions
        function showCustomerShop() {
            hideSections();
            document.getElementById('customer-shop-section').style.display = 'block';
            document.getElementById('customer-shop-section').scrollIntoView({ behavior: 'smooth' });
            updateSidebarActive('customer-shop');
        }

        function showCustomerCart() {
            hideSections();
            document.getElementById('customer-cart-section').style.display = 'block';
            document.getElementById('customer-cart-section').scrollIntoView({ behavior: 'smooth' });
            updateSidebarActive('customer-cart');
        }

        function addToCart(flowerId, flowerName, price) {
            const qtyInput = document.getElementById(flowerId + '-qty');
            const quantity = parseInt(qtyInput.value);

            if (quantity <= 0) {
                alert('Please select a valid quantity');
                return;
            }

            // Check if item already in cart
            const existingItem = cart.find(item => item.id === flowerId);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    id: flowerId,
                    name: flowerName,
                    price: price,
                    quantity: quantity
                });
            }

            updateCartCount();
            updateCartDisplay();

            // Reset quantity input
            qtyInput.value = 1;

            // Show success message
            alert(`🛒 Added ${quantity} ${flowerName}(s) to cart!\n\nTotal items in cart: ${getTotalItems()}`);
        }

        function updateCartCount() {
            const totalItems = getTotalItems();
            document.getElementById('cart-count').textContent = totalItems;
        }

        function getTotalItems() {
            return cart.reduce((total, item) => total + item.quantity, 0);
        }

        function updateCartDisplay() {
            const cartItemsDiv = document.getElementById('cart-items');
            const cartTotalDiv = document.getElementById('cart-total');
            const checkoutBtn = document.getElementById('checkout-btn');

            if (cart.length === 0) {
                cartItemsDiv.innerHTML = `
                    <div class="text-center py-5">
                        <div style="font-size: 4rem; color: #dee2e6;">🛒</div>
                        <h4 class="text-muted">Your cart is empty</h4>
                        <p class="text-muted">Add some beautiful flowers to get started!</p>
                        <button class="btn btn-primary" onclick="showCustomerShop()">Start Shopping</button>
                    </div>
                `;
                cartTotalDiv.style.display = 'none';
                checkoutBtn.disabled = true;
            } else {
                let cartHTML = '<div class="table-responsive"><table class="table"><thead><tr><th>Flower</th><th>Price</th><th>Quantity</th><th>Subtotal</th><th>Action</th></tr></thead><tbody>';

                let total = 0;
                cart.forEach((item, index) => {
                    const subtotal = item.price * item.quantity;
                    total += subtotal;

                    cartHTML += `
                        <tr>
                            <td><strong>${item.name}</strong></td>
                            <td>$${item.price.toFixed(2)}</td>
                            <td>${item.quantity}</td>
                            <td>$${subtotal.toFixed(2)}</td>
                            <td><button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">Remove</button></td>
                        </tr>
                    `;
                });

                cartHTML += '</tbody></table></div>';
                cartItemsDiv.innerHTML = cartHTML;

                document.getElementById('total-amount').textContent = total.toFixed(2);
                cartTotalDiv.style.display = 'block';
                checkoutBtn.disabled = false;
            }
        }

        function removeFromCart(index) {
            const item = cart[index];
            if (confirm(`Remove ${item.name} from cart?`)) {
                cart.splice(index, 1);
                updateCartCount();
                updateCartDisplay();
            }
        }

        function customerCheckout() {
            if (cart.length === 0) {
                alert('Your cart is empty!');
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const itemsList = cart.map(item => `${item.name} x${item.quantity}`).join(', ');

            alert(`🎉 Order Placed Successfully!\n\nItems: ${itemsList}\nTotal: $${total.toFixed(2)}\n\nThank you for your purchase! In the real application, this would process payment and create an order.`);

            // Clear cart
            cart = [];
            updateCartCount();
            updateCartDisplay();
        }
    </script>
</body>
</html>
