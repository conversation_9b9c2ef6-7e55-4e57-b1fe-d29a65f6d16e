<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flower Shop Management - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #28a745 !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #28a745;
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            transition: transform 0.2s ease-in-out;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        .quick-action-card {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .quick-action-card:hover {
            border-color: #28a745;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
        }
        
        .demo-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- Demo Banner -->
    <div class="demo-banner">
        <h4>🌸 Flower Shop Management System - Frontend Demo</h4>
        <p class="mb-0">This is a preview of the web interface. The full Spring Boot application is ready to run!</p>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                🌸 Flower Shop Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        👤 Seller
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                📊 Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#inventory">
                                🌺 Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#orders">
                                📦 Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#create-sale">
                                ➕ Create Sale
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Flowers</div>
                                        <div class="h5 mb-0 font-weight-bold">6</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">🌺</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Orders</div>
                                        <div class="h5 mb-0 font-weight-bold">12</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">📦</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Low Stock</div>
                                        <div class="h5 mb-0 font-weight-bold">2</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">⚠️</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Revenue</div>
                                        <div class="h5 mb-0 font-weight-bold">$2,847</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon">💰</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h3>Quick Actions</h3>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">🌸</div>
                                <h5 class="card-title">Add New Flower</h5>
                                <p class="card-text">Add a new flower to your inventory</p>
                                <button class="btn btn-primary">Add Flower</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">🛒</div>
                                <h5 class="card-title">Create Sale</h5>
                                <p class="card-text">Process a new customer order</p>
                                <button class="btn btn-primary">Create Sale</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">📋</div>
                                <h5 class="card-title">View Inventory</h5>
                                <p class="card-text">Manage your flower inventory</p>
                                <button class="btn btn-primary" onclick="showInventory()">View Inventory</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card quick-action-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3" style="font-size: 3rem;">📊</div>
                                <h5 class="card-title">Order History</h5>
                                <p class="card-text">View all past orders and sales</p>
                                <button class="btn btn-primary">View Orders</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sample Inventory Table (Hidden by default) -->
                <div id="inventory-section" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">🌺 Current Inventory</h5>
                            <button class="btn btn-primary btn-sm">Add New Flower</button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Flower</th>
                                            <th>Price</th>
                                            <th>Quantity</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌹</span>
                                                    <strong>Rose</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$2.50</span></td>
                                            <td><span class="fw-bold">10</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌷</span>
                                                    <strong>Tulip</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$1.80</span></td>
                                            <td><span class="fw-bold">15</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌼</span>
                                                    <strong>Daisy</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$1.20</span></td>
                                            <td><span class="fw-bold">20</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌻</span>
                                                    <strong>Sunflower</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$1.30</span></td>
                                            <td><span class="fw-bold">30</span></td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌺</span>
                                                    <strong>Lily</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$3.00</span></td>
                                            <td><span class="fw-bold">8</span></td>
                                            <td><span class="badge bg-warning">Low Stock</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger">Delete</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2" style="font-size: 1.5rem;">🌸</span>
                                                    <strong>Orchid</strong>
                                                </div>
                                            </td>
                                            <td><span class="fw-bold text-success">$5.00</span></td>
                                            <td><span class="fw-bold">5</span></td>
                                            <td><span class="badge bg-danger">Very Low</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                                <button class="btn btn-sm btn-outline-danger">Delete</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showInventory() {
            document.getElementById('inventory-section').style.display = 'block';
            document.getElementById('inventory-section').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
