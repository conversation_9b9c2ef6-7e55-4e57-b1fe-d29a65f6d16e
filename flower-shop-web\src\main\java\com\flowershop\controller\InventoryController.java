package com.flowershop.controller;

import com.flowershop.model.Flower;
import com.flowershop.service.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.util.List;

@Controller
@RequestMapping("/inventory")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @GetMapping
    public String viewInventory(Model model) {
        List<Flower> flowers = inventoryService.getAllFlowers();
        model.addAttribute("flowers", flowers);
        return "inventory/list";
    }

    @GetMapping("/add")
    public String showAddForm(Model model) {
        model.addAttribute("flower", new Flower());
        return "inventory/add";
    }

    @PostMapping("/add")
    public String addFlower(@Valid @ModelAttribute("flower") Flower flower, 
                           BindingResult result, 
                           RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return "inventory/add";
        }

        try {
            inventoryService.addFlower(flower);
            redirectAttributes.addFlashAttribute("successMessage", "Flower added successfully!");
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
        }

        return "redirect:/inventory";
    }

    @GetMapping("/edit/{id}")
    public String showEditForm(@PathVariable Long id, Model model, RedirectAttributes redirectAttributes) {
        try {
            Flower flower = inventoryService.getFlowerById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Flower not found"));
            model.addAttribute("flower", flower);
            return "inventory/edit";
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
            return "redirect:/inventory";
        }
    }

    @PostMapping("/edit/{id}")
    public String updateFlower(@PathVariable Long id, 
                              @Valid @ModelAttribute("flower") Flower flower, 
                              BindingResult result, 
                              RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return "inventory/edit";
        }

        try {
            inventoryService.updateFlower(id, flower);
            redirectAttributes.addFlashAttribute("successMessage", "Flower updated successfully!");
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
        }

        return "redirect:/inventory";
    }

    @PostMapping("/delete/{id}")
    public String deleteFlower(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            inventoryService.deleteFlower(id);
            redirectAttributes.addFlashAttribute("successMessage", "Flower deleted successfully!");
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
        }

        return "redirect:/inventory";
    }
}
