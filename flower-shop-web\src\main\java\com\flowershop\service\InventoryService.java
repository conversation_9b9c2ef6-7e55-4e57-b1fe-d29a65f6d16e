package com.flowershop.service;

import com.flowershop.model.Flower;
import com.flowershop.repository.FlowerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class InventoryService {

    @Autowired
    private FlowerRepository flowerRepository;

    public List<Flower> getAllFlowers() {
        return flowerRepository.findAll();
    }

    public Optional<Flower> getFlowerById(Long id) {
        return flowerRepository.findById(id);
    }

    public Optional<Flower> getFlowerByName(String name) {
        return flowerRepository.findByName(name);
    }

    public Flower addFlower(Flower flower) {
        if (flowerRepository.existsByName(flower.getName())) {
            throw new IllegalArgumentException("A flower with this name already exists");
        }
        return flowerRepository.save(flower);
    }

    public Flower updateFlower(Long id, Flower flowerDetails) {
        Flower flower = flowerRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Flower not found with id: " + id));

        // Check if name is being changed and if new name already exists
        if (!flower.getName().equals(flowerDetails.getName()) && 
            flowerRepository.existsByName(flowerDetails.getName())) {
            throw new IllegalArgumentException("A flower with this name already exists");
        }

        flower.setName(flowerDetails.getName());
        flower.setPrice(flowerDetails.getPrice());
        flower.setQuantity(flowerDetails.getQuantity());

        return flowerRepository.save(flower);
    }

    public void deleteFlower(Long id) {
        if (!flowerRepository.existsById(id)) {
            throw new IllegalArgumentException("Flower not found with id: " + id);
        }
        flowerRepository.deleteById(id);
    }

    public boolean isFlowerAvailable(Long flowerId, Integer requestedQuantity) {
        Optional<Flower> flower = flowerRepository.findById(flowerId);
        return flower.isPresent() && flower.get().getQuantity() >= requestedQuantity;
    }

    public void reduceFlowerQuantity(Long flowerId, Integer quantity) {
        Flower flower = flowerRepository.findById(flowerId)
                .orElseThrow(() -> new IllegalArgumentException("Flower not found with id: " + flowerId));

        if (flower.getQuantity() < quantity) {
            throw new IllegalArgumentException("Not enough stock available. Current stock: " + flower.getQuantity());
        }

        flower.setQuantity(flower.getQuantity() - quantity);
        flowerRepository.save(flower);
    }

    public void restoreFlowerQuantity(Long flowerId, Integer quantity) {
        Flower flower = flowerRepository.findById(flowerId)
                .orElseThrow(() -> new IllegalArgumentException("Flower not found with id: " + flowerId));

        flower.setQuantity(flower.getQuantity() + quantity);
        flowerRepository.save(flower);
    }
}
