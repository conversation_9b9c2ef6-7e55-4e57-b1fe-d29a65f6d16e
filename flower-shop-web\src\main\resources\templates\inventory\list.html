<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory - Flower Shop Management</title>
    
    <!-- Bootstrap CSS -->
    <link th:href="@{/webjars/bootstrap/5.3.0/css/bootstrap.min.css}" rel="stylesheet">
    
    <!-- Include base styles -->
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #28a745 !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #28a745;
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        
        .badge.bg-success {
            background-color: #28a745 !important;
        }
        
        .badge.bg-warning {
            background-color: #ffc107 !important;
        }
        
        .badge.bg-danger {
            background-color: #dc3545 !important;
        }
        
        .flower-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/dashboard}">
                🌸 Flower Shop Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        👤 Seller
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" th:href="@{/logout}">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/dashboard}">
                                📊 Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/inventory}">
                                🌺 Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders}">
                                📦 Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/orders/create}">
                                ➕ Create Sale
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Flash Messages -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <span class="flower-icon">🌺</span>
                        Flower Inventory
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a th:href="@{/inventory/add}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Flower
                        </a>
                    </div>
                </div>

                <!-- Inventory Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Current Inventory</h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${flowers.empty}" class="text-center py-5">
                            <div style="font-size: 4rem; color: #dee2e6;">🌸</div>
                            <h4 class="text-muted">No flowers in inventory</h4>
                            <p class="text-muted">Start by adding some flowers to your inventory.</p>
                            <a th:href="@{/inventory/add}" class="btn btn-primary">Add First Flower</a>
                        </div>
                        
                        <div th:if="${!flowers.empty}" class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Flower</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="flower : ${flowers}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2" style="font-size: 1.5rem;">🌸</span>
                                                <div>
                                                    <strong th:text="${flower.name}">Rose</strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">$</span>
                                            <span th:text="${#numbers.formatDecimal(flower.price, 1, 2)}">2.50</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold" th:text="${flower.quantity}">10</span>
                                        </td>
                                        <td>
                                            <span th:if="${flower.quantity > 10}" class="badge bg-success">In Stock</span>
                                            <span th:if="${flower.quantity <= 10 and flower.quantity > 5}" class="badge bg-warning">Low Stock</span>
                                            <span th:if="${flower.quantity <= 5}" class="badge bg-danger">Very Low</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a th:href="@{/inventory/edit/{id}(id=${flower.id})}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        data-bs-toggle="modal" 
                                                        th:data-bs-target="'#deleteModal' + ${flower.id}">
                                                    Delete
                                                </button>
                                            </div>
                                            
                                            <!-- Delete Confirmation Modal -->
                                            <div class="modal fade" th:id="'deleteModal' + ${flower.id}" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Confirm Delete</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to delete <strong th:text="${flower.name}">this flower</strong> from inventory?</p>
                                                            <p class="text-muted">This action cannot be undone.</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <form th:action="@{/inventory/delete/{id}(id=${flower.id})}" method="post" style="display: inline;">
                                                                <button type="submit" class="btn btn-danger">Delete</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Inventory Summary -->
                <div th:if="${!flowers.empty}" class="row mt-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Total Flowers</h5>
                                <h2 class="text-primary" th:text="${flowers.size()}">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Total Quantity</h5>
                                <h2 class="text-success" th:text="${#aggregates.sum(flowers.![quantity])}">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Inventory Value</h5>
                                <h2 class="text-info">
                                    $<span th:text="${#numbers.formatDecimal(#aggregates.sum(flowers.![price * quantity]), 1, 2)}">0.00</span>
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script th:src="@{/webjars/jquery/3.7.0/jquery.min.js}"></script>
    <script th:src="@{/webjars/bootstrap/5.3.0/js/bootstrap.bundle.min.js}"></script>
</body>
</html>
