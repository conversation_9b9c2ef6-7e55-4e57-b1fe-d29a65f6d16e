// Flower class inheriting from Product
class Flower extends Product {
    private int quantity;

    public Flower(String name, double price, int quantity) {
        super(name, price);
        this.quantity = quantity;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return name + " - Price: $" + price + ", Quantity: " + quantity;
    }
}