import java.awt.*;
import java.util.List;
import javax.swing.*;

class Order {
    private String orderId;
    private String zipCode;
    private List<Flower> flowers;

    public Order(String orderId, String zipCode, List<Flower> flowers) {
        this.orderId = orderId;
        this.zipCode = zipCode;
        this.flowers = flowers;
    }

    public void printOrderDetails() {
        JFrame frame = new JFrame("Order Details");
        frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        frame.setSize(400, 300);

        JTextArea textArea = new JTextArea();
        textArea.setEditable(false);
        textArea.append("Order ID: " + orderId + "\n");
        textArea.append("Customer ZIP Code: " + zipCode + "\n");
        textArea.append("Flowers Ordered:\n");

        double total = 0;
        for (Flower flower : flowers) {
            double flowerTotal = flower.getPrice() * flower.getQuantity();
            total += flowerTotal;
            textArea.append(flower.getName() + " - Quantity: " + flower.getQuantity() + ", Price: $" + flower.getPrice() + ", Total: $" + flowerTotal + "\n");
        }
        textArea.append("Total Price: $" + total);

        JScrollPane scrollPane = new JScrollPane(textArea);
        frame.add(scrollPane, BorderLayout.CENTER);
        frame.setVisible(true);
    }
}
