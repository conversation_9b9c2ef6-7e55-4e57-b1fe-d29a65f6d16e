import java.util.HashMap;
import java.util.Map;

class LoginManager {
    private Map<String, String> users;

    public LoginManager() {
        users = new HashMap<>();
        // Only a predefined seller user
        users.put("seller", "seller123");  //seller user
    }

    public boolean login(String username, String password) {
        // Check if the provided username and password match the seller's credentials
        if (users.containsKey(username) && users.get(username).equals(password)) {
            return true;
        }
        return false;
    }
}



