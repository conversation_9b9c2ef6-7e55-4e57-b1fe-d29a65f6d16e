package com.flowershop.config;

import com.flowershop.model.Flower;
import com.flowershop.repository.FlowerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private FlowerRepository flowerRepository;

    @Override
    public void run(String... args) throws Exception {
        // Initialize with sample data if database is empty
        if (flowerRepository.count() == 0) {
            initializeSampleFlowers();
        }
    }

    private void initializeSampleFlowers() {
        // Create sample flowers matching your original data
        Flower rose = new Flower("Rose", 2.5, 10);
        Flower tulip = new Flower("Tulip", 1.8, 15);
        Flower daisy = new Flower("Daisy", 1.2, 20);
        Flower sunflower = new Flower("Sunflower", 1.3, 30);
        Flower lily = new Flower("Lily", 3.0, 8);
        Flower orchid = new Flower("Orchid", 5.0, 5);

        flowerRepository.save(rose);
        flowerRepository.save(tulip);
        flowerRepository.save(daisy);
        flowerRepository.save(sunflower);
        flowerRepository.save(lily);
        flowerRepository.save(orchid);

        System.out.println("Sample flower data initialized successfully!");
    }
}
