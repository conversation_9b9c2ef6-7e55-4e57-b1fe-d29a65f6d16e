import java.util.ArrayList;
import java.util.List;

class InventoryManager {
    private List<Flower> inventory;

    public InventoryManager() {
        inventory = new ArrayList<>();
        // Example flowers for the inventory
        inventory.add(new Flower("Rose", 2.5, 10));
        inventory.add(new Flower("Tulip", 1.8, 15));
        inventory.add(new Flower("Daisy", 1.2, 20));
    }

    public List<Flower> getInventory() {
        return inventory;
    }

    public void addFlower(Flower flower) {
        inventory.add(flower);
    }

    public boolean removeFlower(String name) {
        return inventory.removeIf(flower -> flower.getName().equalsIgnoreCase(name));
    }

    public Flower getFlowerByName(String name) {
        for (Flower flower : inventory) {
            if (flower.getName().equalsIgnoreCase(name)) {
                return flower;
            }
        }
        return null;
    }
}







































/*
class InventoryManager {
    private List<Flower> inventory;

    public InventoryManager() {
        inventory = new ArrayList<>();
    }

    public void addFlower(Flower flower) {
        inventory.add(flower);
        System.out.println("Flower added successfully.");
    }

    public void removeFlower(String name) {
        inventory.removeIf(flower -> flower.getName().equalsIgnoreCase(name));
        System.out.println("Flower removed successfully.");
    }

    public void editFlower(String name, double price, int quantity) {
        for (Flower flower : inventory) {
            if (flower.getName().equalsIgnoreCase(name)) {
                flower.setPrice(price);
                flower.setQuantity(quantity);
                System.out.println("Flower updated successfully.");
                return;
            }
        }
        System.out.println("Flower not found.");
    }

    public List<Flower> getInventory() {
        return inventory;
    }
}
*/