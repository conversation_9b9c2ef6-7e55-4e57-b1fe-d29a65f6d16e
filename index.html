<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flower Shop Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .welcome-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            margin: 2rem;
        }
        
        .flower-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .welcome-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .welcome-subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            margin: 0.5rem;
            color: white;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-demo:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
            color: white;
        }
        
        .btn-spring {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .btn-spring:hover {
            box-shadow: 0 10px 25px rgba(23, 162, 184, 0.4);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature h5 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .tech-stack {
            background: #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .tech-stack h6 {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .tech-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .tech-badge {
            background: white;
            color: #495057;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="welcome-card">
        <div class="flower-icon">🌸</div>
        <h1 class="welcome-title">Flower Shop Management</h1>
        <p class="welcome-subtitle">Modern web-based flower inventory and sales management system</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <h5>Secure Login</h5>
                <p>Spring Security authentication</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🌺</div>
                <h5>Inventory</h5>
                <p>Manage flower stock & pricing</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📦</div>
                <h5>Orders</h5>
                <p>Process sales & track history</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h5>Dashboard</h5>
                <p>Business insights & analytics</p>
            </div>
        </div>
        
        <div class="tech-stack">
            <h6>Technology Stack</h6>
            <div class="tech-badges">
                <span class="tech-badge">Spring Boot 3.2</span>
                <span class="tech-badge">Thymeleaf</span>
                <span class="tech-badge">Bootstrap 5</span>
                <span class="tech-badge">Spring Security</span>
                <span class="tech-badge">JPA/Hibernate</span>
                <span class="tech-badge">H2/MySQL</span>
            </div>
        </div>
        
        <div class="d-flex flex-column flex-md-row justify-content-center align-items-center">
            <a href="flower-shop-demo.html" class="btn-demo">
                🌸 View Frontend Demo
            </a>
            <a href="flower-shop-web/" class="btn-demo btn-spring">
                🚀 Run Spring Boot App
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <strong>Demo Credentials:</strong> seller / seller123<br>
                <strong>H2 Console:</strong> <code>http://localhost:8080/h2-console</code>
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
