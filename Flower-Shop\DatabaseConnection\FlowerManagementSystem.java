import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;


public class FlowerManagementSystem {
    private InventoryManager inventoryManager;
    private OrderManager orderManager;
    private Scanner scanner;
    private LoginManager loginManager;

    public FlowerManagementSystem() {
        inventoryManager = new InventoryManager();
        orderManager = new OrderManager();
        scanner = new Scanner(System.in);
        loginManager = new LoginManager();
    }

    public void run() {
        if (!login()) {
            System.out.println("Invalid login attempt. Exiting the system.");
            return;
        }

        boolean running = true;
        while (running) {
            System.out.println("\nFlower Management System");
            System.out.println("1. Create Sale");
            System.out.println("2. Edit Inventory");
            System.out.println("3. View Order History");
            System.out.println("4. View Inventory");
            System.out.println("5. Exit");
            System.out.print("Choose an option: ");
            int choice = scanner.nextInt();
            scanner.nextLine();

            switch (choice) {
                case 1 -> createSale();
                case 2 -> editInventory();
                case 3 -> viewOrderHistory();
                case 4 -> viewInventory();
                case 5 -> running = false;
                default -> System.out.println("Invalid option, try again.");
            }
        }
    }

    private boolean login() {
        System.out.print("Enter username (Only 'seller' is allowed): ");
        String username = scanner.nextLine();
        System.out.print("Enter password: ");
        String password = scanner.nextLine();

        return loginManager.login(username, password);
    }

    private void createSale() {
        List<Flower> selectedFlowers = new ArrayList<>();
        System.out.print("Enter order ID: ");
        String orderId = scanner.nextLine();
        System.out.print("Enter customer ID: ");
        String zipCode = scanner.nextLine();

        boolean addingFlowers = true;
        while (addingFlowers) {
            System.out.print("Enter flower name to add to order (or type 'done' to finish): ");
            String name = scanner.nextLine();
            if (name.equalsIgnoreCase("done")) {
                addingFlowers = false;
                continue;
            }
            System.out.print("Enter quantity: ");
            int quantity = scanner.nextInt();
            scanner.nextLine();

            for (Flower flower : inventoryManager.getInventory()) {
                if (flower.getName().equalsIgnoreCase(name) && flower.getQuantity() >= quantity) {
                    selectedFlowers.add(new Flower(name, flower.getPrice(), quantity));
                    flower.setQuantity(flower.getQuantity() - quantity);
                    System.out.println("Flower added to order.");
                    break;
                }
            }
        }

        // Create the order and show the receipt
        orderManager.createOrder(orderId, zipCode, selectedFlowers);

        // Display the receipt after the order is created
        displayReceipt(orderId, zipCode, selectedFlowers);
    }

    private void displayReceipt(String orderId, String zipCode, List<Flower> selectedFlowers) {
        double totalPrice = 0;
        System.out.println("\n--- Receipt ---");
        System.out.println("Order ID: " + orderId);
        System.out.println("Customer ZIP Code: " + zipCode);
        System.out.println("\nFlowers Ordered:");
        for (Flower flower : selectedFlowers) {
            double flowerTotal = flower.getPrice() * flower.getQuantity();
            totalPrice += flowerTotal;
            System.out.println(flower.getName() + " - Quantity: " + flower.getQuantity() + ", Price: $" + flower.getPrice() + ", Total: $" + flowerTotal);
        }
        System.out.println("\nTotal Price: $" + totalPrice);
        System.out.println("-------------------");
        System.out.println("Thank you for your purchase!");
    }

    private void editInventory() {
        boolean editing = true;
        while (editing) {
            System.out.println("\nEdit Inventory");
            System.out.println("1. Add Flower");
            System.out.println("2. Remove Flower");
            System.out.println("3. Update Flower Price or Quantity");
            System.out.println("4. Back to Main Menu");
            System.out.print("Choose an option: ");
            int choice = scanner.nextInt();
            scanner.nextLine();

            switch (choice) {
                case 1 -> addFlower();
                case 2 -> removeFlower();
                case 3 -> updateFlower();
                case 4 -> editing = false;
                default -> System.out.println("Invalid option, try again.");
            }
        }
    }

    private void addFlower() {
        System.out.print("Enter flower name: ");
        String name = scanner.nextLine();
        System.out.print("Enter flower price: ");
        double price = scanner.nextDouble();
        System.out.print("Enter flower quantity: ");
        int quantity = scanner.nextInt();
        scanner.nextLine();

        inventoryManager.addFlower(new Flower(name, price, quantity));
        System.out.println("Flower added to inventory.");
    }

    private void removeFlower() {
        System.out.print("Enter flower name to remove: ");
        String name = scanner.nextLine();

        boolean removed = inventoryManager.removeFlower(name);
        if (removed) {
            System.out.println("Flower removed from inventory.");
        } else {
            System.out.println("Flower not found.");
        }
    }

    private void updateFlower() {
        System.out.print("Enter flower name to update: ");
        String name = scanner.nextLine();

        Flower flower = inventoryManager.getFlowerByName(name);
        if (flower != null) {
            System.out.print("Enter new price: ");
            double price = scanner.nextDouble();
            System.out.print("Enter new quantity: ");
            int quantity = scanner.nextInt();
            scanner.nextLine();

            flower.setPrice(price);
            flower.setQuantity(quantity);
            System.out.println("Flower updated in inventory.");
        } else {
            System.out.println("Flower not found.");
        }
    }

    private void viewOrderHistory() {
        orderManager.viewOrderHistory();
    }

    // New method to view the inventory
    private void viewInventory() {
        System.out.println("\n--- Inventory ---");
        if (inventoryManager.getInventory().isEmpty()) {
            System.out.println("No flowers available in the inventory.");
        } else {
            for (Flower flower : inventoryManager.getInventory()) {
                System.out.println(flower); // Uses the overridden toString() method from the Flower class
            }
        }
        System.out.println("-----------------");
    }

    public static void main(String[] args) {
        FlowerManagementSystem system = new FlowerManagementSystem();
        system.run();
    }
}

