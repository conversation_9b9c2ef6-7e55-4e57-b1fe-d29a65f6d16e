import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.*;
import java.util.ArrayList;
import java.util.List;

public class MainFrame extends JFrame {
    // Components
    private JTabbedPane tabbedPane;
    private JPanel loginPanel, inventoryPanel, salesPanel, orderHistoryPanel;
    private JTable inventoryTable, orderTable;
    private DefaultTableModel inventoryTableModel, orderTableModel;
    private JTextField usernameField;
    private JPasswordField passwordField;  // Fixed: Changed JTextField to JPasswordField
    private JButton loginButton;
    private JLabel statusLabel;
    
    // System managers
    private InventoryManager inventoryManager;
    private OrderManager orderManager;
    private LoginManager loginManager;
    
    public MainFrame() {
        inventoryManager = new InventoryManager();
        orderManager = new OrderManager();
        loginManager = new LoginManager();
        
        // Create panels first
        createLoginPanel();
        createInventoryPanel();
        createSalesPanel();
        createOrderHistoryPanel();
        
        // Initialize frame after panels are created
        initializeFrame();  // Fixed: Moved after panel creation
        
        // Start with login panel
        setContentPane(loginPanel);
        
        // Configure frame properties
        setTitle("Flower Management System");
        setSize(800, 600);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
    }
    
    private void initializeFrame() {
        tabbedPane = new JTabbedPane();
        tabbedPane.addTab("Inventory", inventoryPanel);
        tabbedPane.addTab("Create Sale", salesPanel);
        tabbedPane.addTab("Order History", orderHistoryPanel);
    }
    
    private void createLoginPanel() {
        loginPanel = new JPanel(new BorderLayout());
        JPanel formPanel = new JPanel(new GridBagLayout());
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        
        JLabel titleLabel = new JLabel("Flower Management System Login", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 20));
        loginPanel.add(titleLabel, BorderLayout.NORTH);
        
        // Username field
        gbc.gridx = 0;
        gbc.gridy = 0;
        formPanel.add(new JLabel("Username:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 0;
        usernameField = new JTextField(15);
        formPanel.add(usernameField, gbc);
        
        // Password field
        gbc.gridx = 0;
        gbc.gridy = 1;
        formPanel.add(new JLabel("Password:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 1;
        passwordField = new JPasswordField(15);  // Fixed: Properly initializing JPasswordField
        formPanel.add(passwordField, gbc);
        
        // Login button
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 2;
        loginButton = new JButton("Login");
        formPanel.add(loginButton, gbc);
        
        // Status label
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 2;
        statusLabel = new JLabel(" ", JLabel.CENTER);
        statusLabel.setForeground(Color.RED);
        formPanel.add(statusLabel, gbc);
        
        loginPanel.add(formPanel, BorderLayout.CENTER);
        
        // Add login button action
        loginButton.addActionListener(e -> {
            String username = usernameField.getText();
            String password = new String(passwordField.getPassword());  // Fixed: Properly getting password from JPasswordField
            
            if (loginManager.login(username, password)) {
                statusLabel.setText("Login successful!");
                setContentPane(tabbedPane);
                validate();
                refreshInventoryTable();
                refreshOrderTable();
            } else {
                statusLabel.setText("Invalid username or password!");
            }
        });
    }
    
    private void createInventoryPanel() {
        inventoryPanel = new JPanel(new BorderLayout());
        
        // Create inventory table
        inventoryTableModel = new DefaultTableModel(
            new Object[][] {},
            new String[] {"Name", "Price", "Quantity"}
        ) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;  // Fixed: Made table cells non-editable
            }
        };
        inventoryTable = new JTable(inventoryTableModel);
        JScrollPane scrollPane = new JScrollPane(inventoryTable);
        inventoryPanel.add(scrollPane, BorderLayout.CENTER);
        
        // Create control panel
        JPanel controlPanel = new JPanel(new FlowLayout());
        
        JButton addButton = new JButton("Add Flower");
        JButton removeButton = new JButton("Remove Flower");
        JButton editButton = new JButton("Edit Flower");
        
        controlPanel.add(addButton);
        controlPanel.add(removeButton);
        controlPanel.add(editButton);
        
        inventoryPanel.add(controlPanel, BorderLayout.SOUTH);
        
        // Add event listeners
        addButton.addActionListener(e -> showAddFlowerDialog());
        removeButton.addActionListener(e -> showRemoveFlowerDialog());
        editButton.addActionListener(e -> showEditFlowerDialog());
    }
    
    private void createSalesPanel() {
        salesPanel = new JPanel(new BorderLayout());
        
        JPanel inputPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        
        // Order ID input
        gbc.gridx = 0;
        gbc.gridy = 0;
        inputPanel.add(new JLabel("Order ID:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 0;
        JTextField orderIdField = new JTextField(15);
        inputPanel.add(orderIdField, gbc);
        
        // ZIP code input
        gbc.gridx = 0;
        gbc.gridy = 1;
        inputPanel.add(new JLabel("ZIP Code:"), gbc);
        
        gbc.gridx = 1;
        gbc.gridy = 1;
        JTextField zipCodeField = new JTextField(15);
        inputPanel.add(zipCodeField, gbc);
        
        // Sale items table
        DefaultTableModel saleItemsModel = new DefaultTableModel(
            new Object[][] {},
            new String[] {"Flower", "Quantity", "Price", "Total"}
        ) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;  // Fixed: Made table cells non-editable
            }
        };
        JTable saleItemsTable = new JTable(saleItemsModel);
        JScrollPane saleItemsScrollPane = new JScrollPane(saleItemsTable);
        
        // Control buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton addItemButton = new JButton("Add Item");
        JButton removeItemButton = new JButton("Remove Item");
        JButton createOrderButton = new JButton("Create Order");
        
        buttonPanel.add(addItemButton);
        buttonPanel.add(removeItemButton);
        buttonPanel.add(createOrderButton);
        
        // Add components to sales panel
        salesPanel.add(inputPanel, BorderLayout.NORTH);
        salesPanel.add(saleItemsScrollPane, BorderLayout.CENTER);
        salesPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        // Add event listeners
        addItemButton.addActionListener(e -> {
            // Fixed: Check if there are flowers available in inventory
            if (inventoryManager.getInventory().isEmpty()) {
                JOptionPane.showMessageDialog(this, "No flowers available in inventory");
                return;
            }
            showAddSaleItemDialog(saleItemsModel);
        });
        
        removeItemButton.addActionListener(e -> {
            int selectedRow = saleItemsTable.getSelectedRow();
            if (selectedRow != -1) {
                saleItemsModel.removeRow(selectedRow);
            } else {
                JOptionPane.showMessageDialog(this, "Please select an item to remove");  // Fixed: Added error message
            }
        });
        
        createOrderButton.addActionListener(e -> {
            String orderId = orderIdField.getText().trim();
            String zipCode = zipCodeField.getText().trim();
            
            if (orderId.isEmpty() || zipCode.isEmpty()) {
                JOptionPane.showMessageDialog(this, "Order ID and ZIP Code are required");
                return;
            }
            
            if (saleItemsModel.getRowCount() == 0) {
                JOptionPane.showMessageDialog(this, "Please add items to the order");
                return;
            }
            
            List<Flower> selectedFlowers = new ArrayList<>();
            for (int i = 0; i < saleItemsModel.getRowCount(); i++) {
                String name = (String) saleItemsModel.getValueAt(i, 0);
                int quantity = Integer.parseInt(saleItemsModel.getValueAt(i, 1).toString());
                double price = Double.parseDouble(saleItemsModel.getValueAt(i, 2).toString());
                
                selectedFlowers.add(new Flower(name, price, quantity));
                
                // Update inventory
                Flower flower = inventoryManager.getFlowerByName(name);
                if (flower != null) {
                    flower.setQuantity(flower.getQuantity() - quantity);
                }
            }
            
            orderManager.createOrder(orderId, zipCode, selectedFlowers);
            JOptionPane.showMessageDialog(this, "Order created successfully");
            
            // Clear fields
            orderIdField.setText("");
            zipCodeField.setText("");
            saleItemsModel.setRowCount(0);
            
            refreshInventoryTable();
            refreshOrderTable();
        });
    }
    
    private void createOrderHistoryPanel() {
        orderHistoryPanel = new JPanel(new BorderLayout());
        
        // Create order history table
        orderTableModel = new DefaultTableModel(
            new Object[][] {},
            new String[] {"Order ID", "ZIP Code", "Items", "Total"}
        ) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;  // Fixed: Made table cells non-editable
            }
        };
        orderTable = new JTable(orderTableModel);
        JScrollPane scrollPane = new JScrollPane(orderTable);
        orderHistoryPanel.add(scrollPane, BorderLayout.CENTER);
        
        // Refresh button
        JButton refreshButton = new JButton("Refresh");
        refreshButton.addActionListener(e -> refreshOrderTable());
        
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.add(refreshButton);
        orderHistoryPanel.add(buttonPanel, BorderLayout.SOUTH);
    }
    
    private void showAddFlowerDialog() {
        JTextField nameField = new JTextField(15);
        JTextField priceField = new JTextField(15);
        JTextField quantityField = new JTextField(15);
        
        JPanel panel = new JPanel(new GridLayout(3, 2));
        panel.add(new JLabel("Name:"));
        panel.add(nameField);
        panel.add(new JLabel("Price:"));
        panel.add(priceField);
        panel.add(new JLabel("Quantity:"));
        panel.add(quantityField);
        
        int result = JOptionPane.showConfirmDialog(this, panel, "Add Flower", JOptionPane.OK_CANCEL_OPTION);
        if (result == JOptionPane.OK_OPTION) {
            try {
                String name = nameField.getText().trim();
                double price = Double.parseDouble(priceField.getText().trim());
                int quantity = Integer.parseInt(quantityField.getText().trim());
                
                if (name.isEmpty()) {
                    JOptionPane.showMessageDialog(this, "Name is required");
                    return;
                }
                
                // Fixed: Check if flower with the same name already exists
                if (inventoryManager.getFlowerByName(name) != null) {
                    JOptionPane.showMessageDialog(this, "A flower with this name already exists");
                    return;
                }
                
                inventoryManager.addFlower(new Flower(name, price, quantity));
                refreshInventoryTable();
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(this, "Invalid price or quantity");
            }
        }
    }
    
    private void showRemoveFlowerDialog() {
        int selectedRow = inventoryTable.getSelectedRow();
        if (selectedRow != -1) {
            String name = (String) inventoryTableModel.getValueAt(selectedRow, 0);
            int result = JOptionPane.showConfirmDialog(this, "Remove " + name + " from inventory?", "Remove Flower", JOptionPane.YES_NO_OPTION);
            
            if (result == JOptionPane.YES_OPTION) {
                inventoryManager.removeFlower(name);
                refreshInventoryTable();
            }
        } else {
            JOptionPane.showMessageDialog(this, "Please select a flower to remove");
        }
    }
    
    private void showEditFlowerDialog() {
        int selectedRow = inventoryTable.getSelectedRow();
        if (selectedRow != -1) {
            String name = (String) inventoryTableModel.getValueAt(selectedRow, 0);
            Flower flower = inventoryManager.getFlowerByName(name);
            
            if (flower != null) {
                JTextField priceField = new JTextField(String.valueOf(flower.getPrice()), 15);
                JTextField quantityField = new JTextField(String.valueOf(flower.getQuantity()), 15);
                
                JPanel panel = new JPanel(new GridLayout(2, 2));
                panel.add(new JLabel("Price:"));
                panel.add(priceField);
                panel.add(new JLabel("Quantity:"));
                panel.add(quantityField);
                
                int result = JOptionPane.showConfirmDialog(this, panel, "Edit " + name, JOptionPane.OK_CANCEL_OPTION);
                if (result == JOptionPane.OK_OPTION) {
                    try {
                        double price = Double.parseDouble(priceField.getText().trim());
                        int quantity = Integer.parseInt(quantityField.getText().trim());
                        
                        // Fixed: Validate quantity is not negative
                        if (price < 0 || quantity < 0) {
                            JOptionPane.showMessageDialog(this, "Price and quantity must be positive values");
                            return;
                        }
                        
                        flower.setPrice(price);
                        flower.setQuantity(quantity);
                        refreshInventoryTable();
                    } catch (NumberFormatException e) {
                        JOptionPane.showMessageDialog(this, "Invalid price or quantity");
                    }
                }
            }
        } else {
            JOptionPane.showMessageDialog(this, "Please select a flower to edit");
        }
    }
    
    private void showAddSaleItemDialog(DefaultTableModel model) {
        // Create a list of available flowers from inventory
        List<Flower> inventory = inventoryManager.getInventory();
        if (inventory.isEmpty()) {  // Fixed: Check if inventory is empty
            JOptionPane.showMessageDialog(this, "No flowers available in inventory");
            return;
        }
        
        String[] flowerNames = inventory.stream()
                                      .map(Flower::getName)
                                      .toArray(String[]::new);
        
        JComboBox<String> flowerCombo = new JComboBox<>(flowerNames);
        JTextField quantityField = new JTextField(10);
        
        JPanel panel = new JPanel(new GridLayout(2, 2));
        panel.add(new JLabel("Flower:"));
        panel.add(flowerCombo);
        panel.add(new JLabel("Quantity:"));
        panel.add(quantityField);
        
        int result = JOptionPane.showConfirmDialog(this, panel, "Add Item to Sale", JOptionPane.OK_CANCEL_OPTION);
        if (result == JOptionPane.OK_OPTION) {
            try {
                String selectedFlower = (String) flowerCombo.getSelectedItem();
                int quantity = Integer.parseInt(quantityField.getText().trim());
                
                // Fixed: Validate quantity is positive
                if (quantity <= 0) {
                    JOptionPane.showMessageDialog(this, "Quantity must be greater than zero");
                    return;
                }
                
                Flower flower = inventoryManager.getFlowerByName(selectedFlower);
                if (flower != null) {
                    if (flower.getQuantity() >= quantity) {
                        double price = flower.getPrice();
                        double total = price * quantity;
                        
                        model.addRow(new Object[]{selectedFlower, quantity, price, total});
                    } else {
                        JOptionPane.showMessageDialog(this, "Not enough stock available. Current stock: " + flower.getQuantity());  // Fixed: Added current stock information
                    }
                }
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(this, "Invalid quantity");
            }
        }
    }
    
    private void refreshInventoryTable() {
        inventoryTableModel.setRowCount(0);
        
        for (Flower flower : inventoryManager.getInventory()) {
            inventoryTableModel.addRow(new Object[]{
                flower.getName(),
                flower.getPrice(),
                flower.getQuantity()
            });
        }
    }
    
    private void refreshOrderTable() {
        // This implementation is a limitation of the current OrderManager
        // which doesn't provide direct access to the order list
        // Instead, we'll simply clear the table for now
        orderTableModel.setRowCount(0);
        
        // Note: A proper implementation would iterate through orders in OrderManager
        // but we would need to modify OrderManager to expose the order list
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            MainFrame frame = new MainFrame();
            frame.setVisible(true);
        });
    }
}